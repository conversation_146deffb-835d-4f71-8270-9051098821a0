<?php

namespace Invato\Warmtepompen;

use Invato\Warmtepompen\models\WarmtepompSettings;
use System\Classes\PluginBase;

/**
 * Plugin Information File
 *
 * @link https://docs.octobercms.com/3.x/extend/system/plugins.html
 */
class Plugin extends PluginBase
{
    public function pluginDetails()
    {
        return [
            'name' => 'Warmtepompen',
            'description' => 'No description provided yet...',
            'author' => 'Invato',
            'icon' => 'icon-leaf',
        ];
    }

    public function boot()
    {
        //
    }

    public function registerComponents()
    {
        return [
            'Invato\Warmtepompen\components\WarmtepompCheck' => 'WarmtepompCheck',
        ];
    }

    public function registerPageSnippets()
    {
        return [
            'Invato\Warmtepompen\components\WarmtepompCheck' => 'WarmtepompCheck',
        ];
    }

    public function registerPermissions()
    {
        return [
            'invato.Warmtepompen.manage_plugin' => [
                'tab' => 'Invato',
                'label' => 'Beheer Warmtepompen',
            ],
        ];
    }

    public function registerMailTemplates()
    {
        return [
            'invato.warmtepompen:warmtepomp-consumer' => 'invato.warmtepompen::mail.warmtepomp-consumer',
            'invato.warmtepompen:warmtepomp-company' => 'invato.warmtepompen::mail.warmtepomp-company',
        ];
    }

    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'Warmtepomp instellingen',
                'description' => 'Beheer warmtepomp instellingen',
                'category' => 'Plugins',
                'icon' => 'ph ph-list-checks',
                'size' => 'adaptive',
                'class' => WarmtepompSettings::class,
            ],
        ];
    }
}
