<?php

namespace Invato\Careers\Models;

use Backend\Models\ImportModel;
use Exception;
use Illuminate\Support\Str;

class VacancyImportModel extends ImportModel
{
    public $rules = [
        'title' => 'required|string|max:255',
        'location' => 'nullable|string|max:255',
        'hours' => 'nullable|string|max:255',
        'is_active' => 'boolean',
    ];

    public function getColumns()
    {
        return [
            'title' => 'Title',
            'slug' => 'Slug',
            'location' => 'Location',
            'hours' => 'Hours',
            'tasks' => 'Tasks',
            'start_date' => 'Start Date',
            'description_top' => 'Description Top',
            'requirements' => 'Requirements (JSON)',
            'offer' => 'Offer (JSON)',
            'description_bottom' => 'Description Bottom',
            'is_active' => 'Is Active',
            'external_id' => 'External ID',
            'external_data' => 'External Data (JSON)',
        ];
    }

    public function importData($results, $sessionKey = null): void
    {
        foreach ($results as $row => $data) {
            try {
                // Check if vacancy with this title/slug already exists
                $slug = $data['slug'] ?? Str::slug($data['title']);
                $vacancy = Vacancy::withTrashed()->where('slug', $slug)->first();

                if (! $vacancy) {
                    $vacancy = new Vacancy;
                }

                $vacancy->fill([
                    'title' => $data['title'] ?? null,
                    'slug' => $slug ?? null,
                    'location' => $data['location'] ?? null,
                    'hours' => $data['hours'] ?? null,
                    'tasks' => $data['tasks'] ?? null,
                    'start_date' => !empty($data['start_date']) ? $data['start_date'] : null,
                    'description_top' => $data['description_top'] ?? null,
                    'description_bottom' => $data['description_bottom'] ?? null,
                    'is_active' => isset($data['is_active']) ? (bool) $data['is_active'] : true,
                    'external_id' => $data['external_id'] ?? null,
                ]);

                // Handle arrays
                if (! empty($data['requirements'])) {
                    if (is_string($data['requirements'])) {
                        try {
                            // Parse JSON if it's a string
                            $vacancy->requirements = json_decode($data['requirements'], true, 512,
                                JSON_THROW_ON_ERROR) ?: [];
                        } catch (Exception $e) {
                            $vacancy->requirements = [];
                        }
                    } else {
                        $vacancy->requirements = $data['requirements'];
                    }
                }

                if (! empty($data['offer'])) {
                    if (is_string($data['offer'])) {
                        try {
                            // Parse JSON if it's a string
                            $vacancy->offer = json_decode($data['offer'], true, 512,
                                JSON_THROW_ON_ERROR) ?: [];
                        } catch (Exception $e) {
                            $vacancy->offer = [];
                        }
                    } else {
                        $vacancy->offer = $data['offer'];
                    }
                }

                if (! empty($data['external_data'])) {
                    if (is_string($data['external_data'])) {
                        try {
                            // Parse JSON if it's a string
                            $vacancy->external_data = json_decode($data['external_data'], true, 512,
                                JSON_THROW_ON_ERROR) ?: [];
                        } catch (Exception $e) {
                            $vacancy->external_data = [];
                        }
                    } else {
                        $vacancy->external_data = $data['external_data'];
                    }
                }

                $vacancy->save();

                $this->logCreated();
            } catch (Exception $ex) {
                $this->logError($row, $ex->getMessage());
            }
        }
    }
}
