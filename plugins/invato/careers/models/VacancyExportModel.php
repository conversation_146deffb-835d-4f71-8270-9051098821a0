<?php

namespace Invato\Careers\Models;

use Backend\Models\ExportModel;
use JsonException;

class VacancyExportModel extends ExportModel
{
    public function getColumns()
    {
        return [
            'id' => 'ID',
            'title' => 'Title',
            'slug' => 'Slug',
            'location' => 'Location',
            'hours' => 'Hours',
            'tasks' => 'Tasks',
            'start_date' => 'Start Date',
            'description_top' => 'Description Top',
            'requirements' => 'Requirements (JSON)',
            'offer' => 'Offer (JSON)',
            'description_bottom' => 'Description Bottom',
            'is_active' => 'Is Active',
            'external_id' => 'External ID',
            'external_data' => 'External Data (JSON)',
            'created_at' => 'Created At',
            'updated_at' => 'Updated At',
        ];
    }

    /**
     * @throws JsonException
     */
    public function exportData($columns, $sessionKey = null): array
    {
        $vacancies = Vacancy::all();
        $exportData = [];

        foreach ($vacancies as $vacancy) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                match ($column) {
                    'requirements',
                    'offer',
                    'external_data' => $record[$column] = $vacancy->{$column} ? json_encode($vacancy->{$column}, JSON_THROW_ON_ERROR) : null,
                    'start_date' => $record[$column] = $vacancy->start_date ? (is_string($vacancy->start_date) ? $vacancy->start_date : $vacancy->start_date->format('Y-m-d')) : null,
                    'is_active' => $record[$column] = $vacancy->is_active ? '1' : '0',
                    default => $record[$column] = $vacancy->{$column},
                };
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
