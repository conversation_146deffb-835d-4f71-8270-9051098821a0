<?php

namespace Invato\Careers\Models;

use Backend\Models\ExportModel;
use JsonException;

class VacancyExportModel extends ExportModel
{
    /**
     * @throws JsonException
     */
    public function exportData($columns, $sessionKey = null): array
    {
        $vacancies = Vacancy::all();
        $exportData = [];

        foreach ($vacancies as $vacancy) {
            $record = [];

            foreach ($columns as $column) {
                // Handle special cases
                match ($column) {
                    'requirements',
                    'offer',
                    'external_data' => $record[$column] = json_encode($vacancy->{$column},
                        JSON_THROW_ON_ERROR),
                    'start_date' => $record[$column] = $vacancy->start_date ? $vacancy->start_date->format('Y-m-d') : null,
                    'is_active' => $record[$column] = $vacancy->is_active ? '1' : '0',
                    default => $record[$column] = $vacancy->{$column},
                };
            }

            $exportData[] = $record;
        }

        return $exportData;
    }
}
