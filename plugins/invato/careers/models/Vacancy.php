<?php

namespace Invato\Careers\Models;

use Invato\Careers\Components\VacancyDetail;
use Invato\Redirects\traits\CanRedirectModelTrait;
use Invato\Seo\traits\HasSeoableTrait;
use Invato\Traits\HasPageFinderTrait;
use Model;
use October\Rain\Database\Traits\Sluggable;
use October\Rain\Database\Traits\SoftDelete;
use October\Rain\Database\Traits\Sortable;
use October\Rain\Database\Traits\Validation;
use RainLab\Translate\Behaviors\TranslatableModel;

/**
 * Model
 */
class Vacancy extends Model
{
    // Begin Skeleton model
    use CanRedirectModelTrait;
    use HasPageFinderTrait;
    use HasSeoableTrait;
    use Sluggable;
    use SoftDelete;
    use Sortable;
    use Validation;

    protected static function booted(): void
    {
        static::deleting(static function ($vacancy) {
            static::createRedirect(
                plugin: 'careers',
                modelRecord: $vacancy,
                detailPageController: VacancyDetail::class,
                status: 301
            );
        });

        static::restored(static function ($vacancy) {
            static::deleteRedirect($vacancy);
        });
    }

    public $table = 'invato_careers_vacancies';

    // https://laravel.com/docs/10.x/eloquent-mutators#attribute-casting
    protected $casts = [
        'id' => 'integer',
        'external_id' => 'string',
        'slug' => 'string',
        'title' => 'string',
        'location' => 'string',
        'hours' => 'string',
        'tasks' => 'string',
        'start_date' => 'date',
        'description_top' => 'string',
        'description_bottom' => 'string',
        'is_active' => 'boolean',
        'synced_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    protected $fillable = [
        'external_id',
        'title',
        'slug',
        'location',
        'hours',
        'tasks',
        'start_date',
        'description_top',
        'requirements',
        'offer',
        'description_bottom',
        'is_active',
        'synced_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    // https://laravel.com/docs/10.x/validation#available-validation-rules
    public $rules = [
        'title' => ['required', 'string', 'max:255'],
        'slug' => ['required', 'string', 'max:255', 'unique:invato_careers_vacancies,slug,{{id}}'],
        'location' => ['nullable', 'string', 'max:255'],
        'hours' => ['nullable', 'string', 'max:255'],
        'tasks' => ['nullable', 'string', 'max:255'],
        'start_date' => ['nullable', 'date'],
        'description_top' => ['nullable', 'string'],
        'description_bottom' => ['nullable', 'string'],
        'is_active' => ['boolean'],
        'external_id' => ['nullable', 'string', 'max:255'],
    ];

    // translatable
    public $implement = [
        TranslatableModel::class
    ];
    public $translatable = [
        'title',
        'slug',
        'location',
        'hours',
        'tasks',
        'description_top',
        'requirements',
        'offer',
        'description_bottom',
    ];

    protected array $slugs = [
        'slug' => 'title',
    ];

    // These attributes should not be in $casts and $rules
    protected $jsonable = [
        'requirements',
        'offer',
        'external_data',
    ];

    /**
     * Get PageFinder configuration for Vacancy model
     */
    protected static function getPageFinderConfig(): array
    {
        return [
            'single_type' => 'careers-vacancy',
            'all_type' => 'all-careers-vacancies',
            'component' => 'VacancyDetail',
        ];
    }

    // END Skeleton model

    // BEGIN Model specific
    // END Model specific
}
