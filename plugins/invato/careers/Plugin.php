<?php

namespace Invato\Careers;

use Invato\Careers\console\GetRegelneefRequestsCommand;
use Invato\Careers\Models\CareersSettings;
use Invato\Careers\Models\RegelneefSettings;
use Invato\Careers\Models\Vacancy;
use Invato\Traits\RegistersPageFinderTrait;
use Invato\Traits\SettingsMenuContextTrait;
use System\Classes\PluginBase;

/**
 * Plugin class
 */
class Plugin extends PluginBase
{
    use RegistersPageFinderTrait;
    use SettingsMenuContextTrait;

    /**
     * register method, called when the plugin is first registered.
     */
    public function register()
    {
        $this->registerConsoleCommand('careers.regelneef', GetRegelneefRequestsCommand::class);
    }

    /**
     * boot method, called right before the request route.
     */
    public function boot(): void
    {
        $this->setupSettingsMenuContext('settings');
        $this->setupSettingsMenuContext('regelneef-settings');
        $this->registerPageFinder();
    }

    /**
     * registerComponents used by the frontend.
     */
    public function registerComponents()
    {
        return [
            'Invato\Careers\Components\VacancyList' => 'CareersVacancyList',
            'Invato\Careers\Components\VacancyDetail' => 'CareersVacancyDetail',
            'Invato\Careers\Components\RegelneefList' => 'CareersRegelneefList',
            'Invato\Careers\Components\RegelneefDetail' => 'CareersRegelneefDetail',
        ];
    }

    /**
     * registerSettings used by the backend.
     */
    public function registerSettings()
    {
        return [
            'settings' => [
                'label' => 'invato.careers::lang.settings.careerssettings',
                'description' => 'invato.careers::lang.settings.careerssettingsdescription',
                'category' => 'Plugins',
                'icon' => 'icon-cog',
                'size' => 'adaptive',
                'class' => CareersSettings::class,

            ],
            'regelneef-settings' => [
                'label' => 'Regelneef Settings',
                'description' => 'Beheer de Regelneef instellingen',
                'category' => 'Plugins',
                'icon' => 'icon-cog',
                'class' => RegelneefSettings::class,
                'size' => 'adaptive',
                'permissions' => ['invato.careers.manage_regelneef'],
            ],
        ];
    }

    public function registerMailTemplates()
    {
        return [
            'invato.careers:apply-consumer' => 'invato.careers::mail.apply-consumer',
            'invato.careers:apply-company' => 'invato.careers::mail.apply-company',
        ];
    }

    public function registerSchedule($schedule)
    {
        if (! empty(RegelneefSettings::get('api_token'))) {
            $schedule->command('careers:regelneef-requests')
                ->timezone('Europe/Amsterdam')
                ->hourly();
        }
    }

    /**
     * Get PageFinder configuration for this plugin
     */
    protected function getPageFinderConfig(): array
    {
        return [
            'model' => Vacancy::class,
            'menu_types' => [
                'careers-vacancy' => 'invato.careers::lang.menuitem.careers_vacancy',
                'all-careers-vacancies' => 'invato.careers::lang.menuitem.all_careers_vacancies',
            ],
        ];
    }
}
