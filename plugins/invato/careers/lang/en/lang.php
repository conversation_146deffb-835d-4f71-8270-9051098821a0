<?php

return [
    'plugin' => [
        'name' => 'Jobs',
        'description' => '',
        'manage-plugin' => 'Manage plugin',
    ],
    'columns' => [
        'id' => 'ID',
        'title' => 'Title',
        'start_date' => 'Start date',
        'is_active' => 'Is active',
        'created_at' => 'Created at',
        'updated_at' => 'Updated at',
        'deleted_at' => 'Deleted at',
        'actions' => 'Actions',
    ],
    'vacancy' => [
        'title' => 'Title',
        'slug' => 'URL',
        'location' => 'Location',
        'hours' => 'Hours per week',
        'tasks' => 'Tasks',
        'start-date' => 'Start date',
        'description-top' => 'Description',
        'requirements' => 'Requirements',
        'offer' => 'What we offer',
        'description-bottom' => 'Bottom description',
        'is-active' => 'Is active',
        'vacancies' => 'Jobs',
        'duplicate_confirm' => 'Are you sure you want to duplicate this vacancy?',
        'duplicate_success' => 'Vacancy duplicated successfully.',
        'manage-vacancies' => 'Manage careers',
    ],
    'tasks' => [
        'description' => 'Example: Programming',
    ],
    'requirement' => [
        'requirement' => 'Requirement',
    ],
    'offer' => [
        'offer' => 'Offer',
    ],
    'settings' => [
        'careerssettings' => 'Jobs settings',
        'careerssettingsdescription' => 'Manage global jobs settings',
        'careerspages' => 'Jobs overview page',
        'vacancypage' => 'Job Page',
    ],
    'setting' => [
        'careerspage' => 'Jobs overview page',
    ],
    'global' => [
        'duplicate' => 'Duplicate',
        'delete' => 'Delete',
    ],
    'permissions' => [
        'manage_plugin' => 'Manage plugin',
        'import_vacancies' => 'Import vacancies',
        'export_vacancies' => 'Export vacancies',
        'manage_regelneef' => 'Manage Regelneef',
    ],
    'menu' => [
        'vacancies' => 'Vacancies',
    ],
    'menuitem' => [
        'careers_vacancy' => 'Vacancy',
        'all_careers_vacancies' => 'All vacancies',
    ],
];
