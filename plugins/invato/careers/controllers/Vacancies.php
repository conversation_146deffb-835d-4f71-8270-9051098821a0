<?php

namespace Invato\Careers\Controllers;

use Backend\Behaviors\FormController;
use Backend\Behaviors\ImportExportController;
use Backend\Behaviors\ListController;
use Backend\Classes\Controller;
use BackendMenu;
use Invato\Careers\Models\Vacancy;
use Invato\PluginDuplicateTrait;
use Invato\PluginImportExportTrait;
use Invato\PluginSoftDeleteTrait;

class Vacancies extends Controller
{
    use PluginDuplicateTrait;
    use PluginImportExportTrait;
    use PluginSoftDeleteTrait;

    public $implement = [
        FormController::class,
        ListController::class,
        ImportExportController::class,
    ];

    public static string $modelClass = Vacancy::class;

    public string $formConfig = 'config_form.yaml';

    public string $listConfig = 'config_list.yaml';

    public string $importExportConfig = 'config_import_export.yaml';

    public string $importPermission = 'invato.careers.import_vacancies';

    public string $exportPermission = 'invato.careers.export_vacancies';

    public $requiredPermissions = [
        'invato.careers.manage_plugin',
    ];

    public function __construct()
    {
        parent::__construct();
        BackendMenu::setContext('Invato.Careers', 'careers', 'vacancies');
    }
}
