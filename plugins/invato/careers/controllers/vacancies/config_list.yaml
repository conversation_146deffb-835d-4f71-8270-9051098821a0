list: $/invato/careers/models/vacancy/columns.yaml
modelClass: Invato\Careers\Models\Vacancy
title: Vacatures
noRecordsMessage: 'backend::lang.list.no_records'
showSetup: true
showCheckboxes: true
recordsPerPage: 20
toolbar:
    buttons: list_toolbar
    search:
        prompt: 'backend::lang.list.search_prompt'
recordUrl: 'invato/careers/vacancies/update/:id'
# BEGIN Skeleton Soft Deletes
filter: $/invato/careers/models/vacancy/scopes.yaml
# END Skeleton Soft Deletes
