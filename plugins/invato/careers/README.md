# Interne documentatie

## Doel:

Het tonen van een overzicht van vacatures die het bedrij<PERSON> a<PERSON>, met een sollicitatie formulier om leads te verzamelen. De plugin ondersteunt ook integratie met externe recruitment systemen zoals Regelneef.

## Backend:

De plugin heeft 2 backend pagina's, die in het linkermenu onder kopje Careers vallen: Vacatures en Instellingen.

### Vacatures pagina:

Hier kunnen vacatures worden beheerd. Elke vacature heeft de volgende velden:
- Titel
- Slug (automatisch gegenereerd)
- Locatie
- Uren (bijv. fulltime, parttime)
- Taken
- Startdatum
- Beschrijving boven
- Vereisten (repeater veld)
- Wat wij bieden (repeater veld)
- Beschrijving onder
- Actief status
- Externe ID (voor synchronisatie)
- Externe data (JSON veld voor extra data)

### Wie heeft toegang?

* Invato
* <PERSON><PERSON> met toegang tot de plugin.

### Instellingen

Hier staan de opties voor de plugin, zoals de pagina die als overzicht van vacatures wordt gebruikt. Te<PERSON><PERSON> kan hier het sollicitatie formulier ingesteld worden.

### Regelneef Instellingen

Specifieke instellingen voor de Regelneef integratie, inclusief API token en synchronisatie opties.

### Wie heeft toegang?

* Invato

## Front-end:

Deze plugin bevat componenten die als overzichtpagina en detailpagina te gebruiken zijn. Verder heeft deze plugin CMS pagina's voor statische inhoud: vacature detail pagina en vacature overzicht pagina.

### Componenten:

- **VacancyList**: Toont een overzicht van alle actieve vacatures
- **VacancyDetail**: Toont de details van een specifieke vacature
- **RegelneefList**: Toont vacatures van Regelneef
- **RegelneefDetail**: Toont details van een Regelneef vacature

### Wie heeft toegang?

* Invato
* Klant met toegang tot de plugin.

## Plugin uitbreiden (Extensie plugin)

De careers plugin is met een extensie plugin gemakkelijk uit te breiden met nieuwe functies, componenten en extra velden.

### Extra velden toevoegen

Om extra velden aan te maken, is er een speciaal JSON veld in de database aangemaakt, zodat er geen migrations uitgevoerd hoeven te worden. Dit is het 'external_data' veld.

## Import/Export functionaliteit

De plugin ondersteunt import en export van vacatures via CSV bestanden. Dit maakt het mogelijk om:
- Vacatures in bulk te importeren
- Bestaande vacatures te exporteren voor backup of migratie
- Data uit te wisselen met externe systemen

## Soft Delete functionaliteit

Vacatures worden niet permanent verwijderd maar krijgen een 'deleted_at' timestamp. Dit maakt het mogelijk om:
- Verwijderde vacatures te herstellen
- Een audit trail bij te houden
- Data integriteit te behouden

## Externe integraties

### Regelneef

De plugin ondersteunt synchronisatie met Regelneef via hun API. Dit gebeurt automatisch via een scheduled command die elk uur draait.

## Gebruikte permissies:

- `invato.careers.manage_plugin` - Algemeen beheer van de plugin
- `invato.careers.import_vacancies` - Import van vacatures
- `invato.careers.export_vacancies` - Export van vacatures
- `invato.careers.manage_regelneef` - Beheer van Regelneef instellingen
- `superusers.view_readme` - Toegang tot deze README
