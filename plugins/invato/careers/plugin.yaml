plugin:
    name: 'invato.careers::lang.plugin.name'
    description: 'invato.careers::lang.plugin.description'
    author: Invato
    icon: oc-icon-suitcase
    homepage: ''
permissions:
    'invato.careers.manage_plugin':
        tab: 'invato.careers::lang.plugin.name'
        label: 'invato.careers::lang.permissions.manage_plugin'
    'invato.careers.import_vacancies':
        tab: 'invato.careers::lang.plugin.name'
        label: 'invato.careers::lang.permissions.import_vacancies'
    'invato.careers.export_vacancies':
        tab: 'invato.careers::lang.plugin.name'
        label: 'invato.careers::lang.permissions.export_vacancies'
    'invato.careers.manage_regelneef':
        tab: 'invato.careers::lang.plugin.name'
        label: 'invato.careers::lang.permissions.manage_regelneef'
navigation:
    careers:
        label: 'invato.careers::lang.plugin.name'
        url: /
        icon: icon-suitcase
        iconSvg: plugins/invato/careers/assets/images/invato-careers.svg
        permissions:
            - 'invato.careers.manage_plugin'
        sideMenu:
            content-section:
                label: 'Content'
                itemType: section

            vacancies:
                label: 'invato.careers::lang.menu.vacancies'
                url: invato/careers/vacancies
                icon: icon-suitcase
                permissions:
                    - 'invato.careers.manage_plugin'

            settings-section:
                label: 'Settings'
                itemType: section

            settings:
                label: 'General'
                url: system/settings/update/invato/careers/settings
                icon: icon-cogs
                permissions:
                    - 'invato.careers.manage_plugin'

            regelneef-settings:
                label: 'Regelneef'
                url: system/settings/update/invato/careers/regelneef-settings
                icon: icon-cogs
                permissions:
                    - 'invato.careers.manage_regelneef'

            documentation-section:
                label: 'Documentation'
                itemType: section

            readme:
                label: 'Readme'
                url: invato/careers/readme
                icon: icon-book
                permissions:
                    - 'superusers.view_readme'
            manual:
                label: 'Manual'
                url: invato/careers/manual
                icon: icon-book
                permissions:
                    - 'invato.careers.manage_plugin'
